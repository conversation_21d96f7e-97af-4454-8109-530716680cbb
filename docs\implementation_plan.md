# Pixel Pastures - Master Implementation Plan

**Version:** 3.0
**Date:** August 31, 2025
**Author:** Gemini

## 1. Introduction

This document provides a definitive, step-by-step implementation plan for developing "Pixel Pastures" from its current state into the full game envisioned in `Full_Idea.txt`. It is a living document that will guide all development efforts.

### 1.1. Guiding Philosophy

The core philosophy of this plan is the direct implementation of the **Entity-Component-System (ECS)** architecture, as detailed in `docs/architectural_proposal.md`. Every step is designed to transform the existing object-oriented codebase into a data-driven, modular, and highly testable engine. This approach will enable the incremental and stable addition of complex features.

### 1.2. Current State Analysis (v0.3.1)

An audit of the current codebase reveals a strong but non-scalable proof-of-concept. The architecture is a traditional Object-Oriented Programming (OOP) model with large classes (`EnhancedFarmWorld`, `Player`) that tightly couple data and logic. This makes adding new, cross-cutting features difficult without significant refactoring.

**Key Implemented Features:**
- A sophisticated rendering engine using Textual, supporting Braille and multiple view modes.
- A smooth-scrolling camera.
- A player character with movement and basic interaction capabilities (tilling, watering, etc.).
- A time system with day/night cycles and seasons.
- A crop system with growth stages and watering mechanics.

This plan's first phase is dedicated to refactoring these existing features into the new ECS paradigm. Subsequent phases will build upon this new foundation to add features modularly.

### 1.3. Testing Philosophy

The ECS architecture is inherently testable. Each **Processor** (System) is a self-contained unit of logic. We can test each one in isolation by:
1.  Creating a temporary `esper.World` for the test.
2.  Creating mock entities with specific components needed by the processor.
3.  Running the processor.
4.  Asserting that the components on the mock entities have been modified as expected.

This allows for robust unit testing of game mechanics without needing to run the full application, which is key to stable, incremental development.

---

## 2. Phase 1: Core Engine Refactoring

**Objective:** Rebuild all existing functionality using the ECS architecture. By the end of this phase, the game should look and feel identical to the current version, but its internal structure will be completely modernized.

### Milestone 1.1: Foundation Setup

**Description:** Prepare the project structure, add dependencies, and initialize the core ECS world and event bus.

**Architectural Guidance:** This is the foundational step. We are creating the central `World` object from the `esper` library that will manage all our entities and components. We also create a simple `EventSystem` to handle communication *between* processors, ensuring they remain decoupled, as per the architectural proposal.

**Tasks:**
1.  Add `esper` to `requirements.txt`.
2.  Create the directory structure: `src/engine/` and `src/ecs/`.
3.  Implement `src/engine/event_system.py` with `EventSystem` and `GameEvent` classes.
4.  Implement `src/ecs/world.py` to initialize and hold the global `esper.World` and `EventSystem` instances.
5.  Create empty files: `src/ecs/components.py` and `src/ecs/processors.py`.
6.  Modify `main.py`: Import the global `world` object. The main game loop's `update` method will now be simplified to a single call: `world.process(delta_time)`.

**Required Reading:**
- `docs/architectural_proposal.md`
- `docs/research/c7_esper_docs.md`
- `docs/research/c7_textual_docs.md`

**Testing:** No specific tests for this setup phase, but the application should run without errors.

### Milestone 1.2: Player & World Rendering Refactor

**Description:** Deconstruct the `Player` and `FarmWorld` classes into ECS entities, components, and processors. The goal is to get a player-controlled character moving on a static map again.

**Architectural Guidance:** We are separating data from logic. The concept of a "Player" is broken down into its constituent parts: it has a `Position` (data), it is `Renderable` (data), and it is controlled by the user (`PlayerInput` data). The *logic* for movement and rendering is moved into dedicated `MovementProcessor` and `RenderProcessor`. This makes the system reusable; the `RenderProcessor` will draw *any* entity with a position and a sprite, not just a player.

**Tasks:**
1.  **Components (`components.py`):** Define `Position(x, y)`, `Renderable(sprite_name, style)`, `Player(stats)`, `PlayerInput`, `Tile(tile_type)`, `IsCollidable`.
2.  **Processors (`processors.py`):**
    *   `InputProcessor`: Reads terminal input and updates the `PlayerInput` component on the player entity.
    *   `MovementProcessor`: Reads `PlayerInput` and updates the `Position` of entities, checking against `IsCollidable` components.
    *   `RenderProcessor`: Reads all entities with `Position` and `Renderable` to draw the world and player using the existing `EnhancedRenderer` utility.
3.  **World Generation:** Create a `world_builder.py` that uses the logic from the old `FarmWorld` to populate the ECS world with tile entities.
4.  **Cleanup:** Delete `src/game/player.py` and `src/game/farm_world.py`.

**Required Reading:**
- `docs/research/c7_esper_docs.md`
- `docs/research/c7_textual_docs.md`
- `docs/research/drawille_library_docs.txt`

**Testing:** Unit test the `MovementProcessor`. Create a mock world with a movable entity and a collidable entity. Send a move command and assert that the movable entity's `Position` component changes correctly, but not if the path is blocked.

### Milestone 1.3: Farming Loop Refactor

**Description:** Re-implement the time, crop, and interaction systems using the ECS model.

**Architectural Guidance:** This milestone demonstrates the power of decoupled systems using the event bus. The `TimeProcessor` is the heart of the game clock. It does not know or care about crops; it simply broadcasts that time has passed. The `CropGrowthProcessor` is a completely separate module that only listens for these time events. This means we can add other time-based systems (e.g., NPC schedules) just by listening to the same events, without ever modifying the `TimeProcessor`.

**Tasks:**
1.  **Components (`components.py`):** Define `GameTimeComponent`, `Crop`, `Soil`.
2.  **Processors (`processors.py`):**
    *   `TimeProcessor`: Updates the global `GameTimeComponent` and **publishes** `HourChanged` and `DayChanged` events via the event bus.
    *   `InteractionProcessor`: Reads `PlayerInput` and publishes interaction events (e.g., `TilledAttempt`).
    *   `FarmingProcessor`: **Subscribes** to interaction events to modify components (e.g., change a `Tile` component and add a `Soil` component).
    *   `CropGrowthProcessor`: **Subscribes** to the `HourChanged` event to update `Crop` components.
3.  **Cleanup:** Delete `src/game/crops.py`, `src/game/time_system.py`, and `src/game/enhanced_farm_world.py`.

**Required Reading:**
- `docs/research/c7_esper_docs.md`
- `docs/research/asyncio_docs.txt`

**Testing:** Unit test the `CropGrowthProcessor`. Create a mock entity with a `Crop` component, send a mock `HourChanged` event, and assert that the crop's growth stage has advanced.

### Milestone 1.4: World Builder Restoration

**Description:** Replace the current basic diagonal-line world generation with the proper farm layout from the original `FarmWorld` class to maintain functional parity with v0.3.1.

**Architectural Guidance:** This milestone ensures that Phase 1 maintains identical visual and functional behavior to the original game. The ECS refactor should be transparent to the player - they should see the same farmhouse, barn, crop areas, water pond, and fence layout that existed before.

**Tasks:**
1.  **World Builder (`src/ecs/world_builder.py`):** Create a world generation system that replicates the original `FarmWorld._get_initial_tile()` logic using ECS entities and components.
2.  **Proper Farm Layout:** Generate the same farmhouse (5,5), barn (50,8), water pond (45-50, 30-34), crop areas (11-24, 21-29), fences, paths, and trees as the original.
3.  **Replace Basic World:** Update `main.py` to use the new world builder instead of the current diagonal pattern generation.
4.  **Visual Verification:** Ensure the game looks identical to the original v0.3.1 farm layout.

**Required Reading:**
- `src/game/farm_world.py` (original implementation)
- `tests/test_farm_world.py` (expected features)

**Testing:** Create integration tests that verify the generated ECS world contains the same key features as the original: farmhouse, barn, water areas, crop zones, and proper tile distribution.

---

## 3. Phase 2: New Feature Implementation

**Objective:** With a stable ECS foundation, we will now add new features as modular sets of components and processors.

### Module 2.1: NPCs and Pathfinding

**Description:** Add non-player characters to the world with basic schedules and movement.

**Architectural Guidance:** NPCs are entities, just like the player, but with an `NPC` component instead of `PlayerInput`. Their behavior is driven by data in a `Schedule` component. The AI logic is split into three distinct processors: one for high-level decisions (`NPCAiProcessor`), one for low-level path calculation (`PathfindingProcessor`), and one for executing the move (`NPCMovementProcessor`). This separation makes each part simpler to develop and test.

**Tasks:**
1.  **Features:** Create 2-3 NPCs with fixed daily schedules.
2.  **Components:** `NPC(name)`, `Schedule(goals)`, `Path(waypoints)`.
3.  **Processors:** `NPCAiProcessor`, `PathfindingProcessor`, `NPCMovementProcessor`.

**Required Reading:**
- `docs/Full_Idea.txt`
- `docs/research/pathfinding_library_docs.txt` (please verify first if this information is relevant)

**Testing:** Unit test the `PathfindingProcessor` with a known grid and obstacles to ensure it finds the shortest valid path.

### Module 2.2: Inventory & Crafting

**Description:** Implement a player inventory and a basic crafting system.

**Architectural Guidance:** Items will be their own entities, but they may not have a `Position` component if they are in an inventory. The player entity will have an `Inventory` component that contains a list of item entity IDs. Crafting is handled by a `CraftingProcessor` that listens for an `InteractedWithCraftingStation` event, decoupling it from the player's direct input.

**Tasks:**
1.  **Features:** Allow the player to hold items. Create a workbench that can combine wood and stone into a fence.
2.  **Components:** `Inventory(items, size)`, `Item(name, quantity)`, `Recipe(ingredients, output)`, `IsCraftingStation`.
3.  **Processors:** `CraftingProcessor`.

**Required Reading:**
- `docs/Full_Idea.txt`

**Testing:** Unit test the `CraftingProcessor`: give a mock player entity an `Inventory` with specific items, send a mock `CraftRequest` event, and assert that the inventory is correctly updated with the crafted item.

### Module 2.3: Persistence (Save & Load)

**Description:** Implement the ability to save and load the entire game state.

**Architectural Guidance:** This is one of the most significant benefits of a data-oriented ECS architecture. The entire state of the game world is just data held in components. The `SaveGameProcessor` can be a "dumb" system that simply iterates through every entity and serializes its components to a file. We will use Pydantic to define schemas for our components to ensure the data is saved and loaded correctly, preventing save file corruption.

**Tasks:**
1.  **Data Models:** Use Pydantic to create a schema for every component.
2.  **Processors:** `SaveGameProcessor` and `LoadGameProcessor`.

**Required Reading:**
- `docs/research/c7_sqlite_docs.md`
- `docs/research/c7_pydantic_docs.md`

**Testing:** This is a critical integration test. The test will: 1. Save the state. 2. Make a copy of the component data. 3. Clear the world. 4. Load the state. 5. Compare the newly loaded world state with the saved copy to ensure a perfect match.

---

## 4. Phase 3: Full Vision Expansion

**Objective:** Implement the remaining advanced features to complete the game's vision as mentioned in `docs/Full_Idea.txt`.

**Description:** This phase will be a series of modules, each developed and tested independently.

**Modules (to be implemented in order):**
- **Economy & Shops:** (New `Shop` components, `EconomyProcessor` to handle price fluctuations).
- **Livestock & Animals:** (New `Animal` components and `AnimalAIProcessor`).
- **Quests & Dialogue:** (New `Quest` and `Dialogue` components, `QuestProcessor`, `DialogueProcessor`).
- **Advanced Systems:** (Genetics, Pests, Weather effects).
- **UI Polish:** (Adding all the UI panels, popups, and menus from the vision).
- **Packaging & Distribution:** (Using PyInstaller or similar to create a distributable binary).

*Each module in this phase will follow the same pattern: define components, create processors, and write isolated tests before integration. The required reading for this entire phase will primarily be `docs/Full_Idea.txt` and the existing technical documentation.*